// ==UserScript==
// @name         NodeSeek 私信优化脚本
// @namespace    https://www.nodeseek.com/
// @version      1.1.0
// @description  NodeSeek 私信记录本地缓存与WebDAV/S3备份
// <AUTHOR>
// @match        https://www.nodeseek.com/notification*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @grant        GM_registerMenuCommand
// @connect      www.nodeseek.com
// @connect      dav.jianguoyun.com
// @connect      *.amazonaws.com
// @connect      *.s3.amazonaws.com
// @connect      s3.*
// @connect      *
// 移除 AWS SDK 依赖，使用 GM_xmlhttpRequest 绕过 CORS
// ==/UserScript==


(function() {
    'use strict';

    /**
     * 应用常量配置
     */
    const Constants = {
        APP_NAME: 'NodeSeek私信优化',
        VERSION: '1.1.0',
        ENCRYPTION_SEED: 'nodeseek_message_optimizer_v1',
        MAX_BACKUPS: 30,
        DEFAULT_TIMEOUT: 30000,
        RETRY_DELAYS: [2000, 4000, 6000],
        HTTP_STATUS: {
            OK: 200,
            UNAUTHORIZED: 401,
            FORBIDDEN: 403,
            NOT_FOUND: 404,
            CONFLICT: 409,
            SERVER_ERROR: 500
        }
    };

    /**
     * 工具函数集合
     */
    const Utils = {
        DEBUG: GM_getValue('debug_mode', false),

        /**
         * 防抖函数
         * @param {Function} func 要防抖的函数
         * @param {number} wait 等待时间（毫秒）
         * @returns {Function} 防抖后的函数
         */
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * 格式化日期为文件名安全的字符串
         * @param {Date} date 要格式化的日期对象
         * @returns {string} 格式化后的日期字符串
         */
        formatDate(date) {
            return date.toISOString().replace(/[:.]/g, '-').slice(0, -5) + '-' + Date.now().toString().slice(-6);
        },

        /**
         * 将UTC时间字符串转换为本地时间字符串
         * @param {string} utcString UTC时间字符串
         * @returns {string} 本地时间字符串
         */
        parseUTCToLocal(utcString) {
            return new Date(utcString).toLocaleString();
        },

        /**
         * 记录日志信息
         * @param {string} message 日志消息
         * @param {string} type 日志类型
         */
        log(message, type = 'info') {
            if (!this.DEBUG && type === 'info') return;
            const typeStr = typeof type === 'string' ? type.toUpperCase() : 'INFO';
            console.log(`[${Constants.APP_NAME}] ${typeStr}: ${message}`);
        },

        /**
         * 记录调试信息
         * @param {string} message 调试消息
         * @param {*} data 可选的数据对象
         */
        debug(message, data = null) {
            if (!this.DEBUG) return;
            if (data !== null) {
                console.log(`[${Constants.APP_NAME}] DEBUG: ${message}`, data);
            } else {
                console.log(`[${Constants.APP_NAME}] DEBUG: ${message}`);
            }
        },

        /**
         * 记录错误信息
         * @param {string} message 错误消息
         * @param {Error|null} error 错误对象
         */
        error(message, error = null) {
            console.error(`[${Constants.APP_NAME}] ERROR: ${message}`, error);
        },

        /**
         * 切换调试模式
         * @param {boolean} enabled 是否启用调试模式
         */
        setDebugMode(enabled) {
            this.DEBUG = enabled;
            GM_setValue('debug_mode', enabled);
            console.log(`[${Constants.APP_NAME}] 调试模式已${enabled ? '开启' : '关闭'}`);
        },

        /**
         * 生成加密密钥
         * @returns {string} 基于用户环境的密钥
         */
        getEncryptionKey() {
            const userAgent = navigator.userAgent;
            const domain = window.location.hostname;
            return btoa(Constants.ENCRYPTION_SEED + userAgent.slice(0, 50) + domain).slice(0, 32);
        },

        /**
         * 简单加密函数
         * @param {string} text 要加密的文本
         * @returns {string} 加密后的文本
         */
        encrypt(text) {
            if (!text) return text;
            try {
                const key = this.getEncryptionKey();
                let result = '';
                for (let i = 0; i < text.length; i++) {
                    const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
                    result += String.fromCharCode(charCode);
                }
                return btoa(result);
            } catch (error) {
                this.error('加密失败', error);
                return text;
            }
        },

        /**
         * 简单解密函数
         * @param {string} encryptedText 要解密的文本
         * @returns {string} 解密后的文本
         */
        decrypt(encryptedText) {
            if (!encryptedText) return encryptedText;
            try {
                const key = this.getEncryptionKey();
                const decoded = atob(encryptedText);
                let result = '';
                for (let i = 0; i < decoded.length; i++) {
                    const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length);
                    result += String.fromCharCode(charCode);
                }
                return result;
            } catch (error) {
                this.error('解密失败', error);
                return encryptedText;
            }
        }
    };

    /**
     * HTTP客户端基类
     * 提供统一的HTTP请求处理
     */
    class HttpClient {
        /**
         * 发送HTTP请求
         * @param {Object} options 请求选项
         * @returns {Promise<Object>} 响应数据
         */
        static async request(options) {
            const {
                method = 'GET',
                url,
                headers = {},
                data,
                timeout = Constants.DEFAULT_TIMEOUT,
                responseType = 'json'
            } = options;

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method,
                    url,
                    headers: {
                        'Accept': 'application/json',
                        'Referer': 'https://www.nodeseek.com/',
                        ...headers
                    },
                    data,
                    timeout,
                    onload: (response) => {
                        try {
                            Utils.debug(`HTTP响应状态: ${response.status}`);
                            Utils.debug(`HTTP响应内容: ${response.responseText.substring(0, 200)}...`);

                            if (response.status < 200 || response.status >= 300) {
                                reject(new Error(`HTTP错误: ${response.status} ${response.statusText}`));
                                return;
                            }

                            if (responseType === 'json') {
                                const data = JSON.parse(response.responseText);
                                if (data.status === 404 && data.message === "USER NOT FOUND") {
                                    reject(new Error('用户未登录'));
                                    return;
                                }
                                resolve(data);
                            } else {
                                resolve(response.responseText);
                            }
                        } catch (e) {
                            Utils.error(`响应解析失败，原始响应: ${response.responseText}`, e);
                            reject(new Error(`响应解析失败: ${e.message}`));
                        }
                    },
                    onerror: (error) => reject(error),
                    ontimeout: () => reject(new Error('请求超时'))
                });
            });
        }

        /**
         * 获取用户友好的错误消息
         * @param {number} status HTTP状态码
         * @param {string} context 上下文描述
         * @returns {string} 错误消息
         */
        static getErrorMessage(status, context = '') {
            const messages = {
                [Constants.HTTP_STATUS.UNAUTHORIZED]: '认证失败，请检查用户名和密码',
                [Constants.HTTP_STATUS.FORBIDDEN]: '权限不足，请检查账户权限',
                [Constants.HTTP_STATUS.NOT_FOUND]: '资源不存在或已被删除',
                [Constants.HTTP_STATUS.CONFLICT]: '操作冲突，请稍后重试',
                [Constants.HTTP_STATUS.SERVER_ERROR]: '服务器内部错误'
            };

            return messages[status] || `${context}失败: HTTP ${status}`;
        }
    }

    /**
     * IndexedDB 数据存储模块
     * 用于本地存储聊天记录数据
     */
    class ChatDB {
        /**
         * 构造函数
         * @param {number} userId 用户ID
         */
        constructor(userId) {
            this.userId = userId;
            this.dbName = `nodeseek_chat_${userId}`;
            this.version = 1;
            this.db = null;
        }

        /**
         * 初始化数据库连接
         * @returns {Promise<void>}
         */
        async init() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.version);

                request.onerror = () => reject(request.error);
                request.onsuccess = () => {
                    this.db = request.result;
                    resolve();
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;

                    if (!db.objectStoreNames.contains('talk_messages')) {
                        const store = db.createObjectStore('talk_messages', { keyPath: 'member_id' });
                        store.createIndex('created_at', 'created_at', { unique: false });
                    }

                    if (!db.objectStoreNames.contains('metadata')) {
                        db.createObjectStore('metadata', { keyPath: 'key' });
                    }
                };
            });
        }

        /**
         * 执行数据库事务操作
         * @param {string} storeName 存储名称
         * @param {string} mode 事务模式
         * @param {Function} operation 操作函数
         * @returns {Promise<*>} 操作结果
         */
        async executeTransaction(storeName, mode, operation) {
            const transaction = this.db.transaction([storeName], mode);
            const store = transaction.objectStore(storeName);

            return new Promise((resolve, reject) => {
                const request = operation(store);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }

        /**
         * 保存聊天消息数据
         * @param {Object} memberData 成员聊天数据
         * @returns {Promise<void>}
         */
        async saveTalkMessage(memberData) {
            return this.executeTransaction('talk_messages', 'readwrite',
                store => store.put(memberData));
        }

        /**
         * 获取指定成员的聊天消息
         * @param {number} memberId 成员ID
         * @returns {Promise<Object|undefined>} 聊天消息数据
         */
        async getTalkMessage(memberId) {
            return this.executeTransaction('talk_messages', 'readonly',
                store => store.get(memberId));
        }

        /**
         * 获取所有聊天消息
         * @returns {Promise<Array>} 所有聊天消息数组
         */
        async getAllTalkMessages() {
            return this.executeTransaction('talk_messages', 'readonly',
                store => store.getAll());
        }

        /**
         * 设置元数据
         * @param {string} key 键名
         * @param {*} value 值
         * @returns {Promise<void>}
         */
        async setMetadata(key, value) {
            return this.executeTransaction('metadata', 'readwrite',
                store => store.put({ key, value }));
        }

        /**
         * 获取元数据
         * @param {string} key 键名
         * @returns {Promise<*>} 元数据值
         */
        async getMetadata(key) {
            const result = await this.executeTransaction('metadata', 'readonly',
                store => store.get(key));
            return result?.value;
        }
    }

    /**
     * NodeSeek API 访问模块
     * 用于与NodeSeek网站API进行交互
     */
    class NodeSeekAPI {
        /**
         * 构造函数
         */
        constructor() {
            this.baseUrl = 'https://www.nodeseek.com/api';
        }

        /**
         * 获取当前用户ID
         * @returns {Promise<number>} 用户ID
         */
        async getUserId() {
            try {
                Utils.debug('正在获取用户ID...');
                const data = await HttpClient.request({
                    url: `${this.baseUrl}/notification/message/with/5230`
                });
                Utils.debug('getUserId API响应:', data);

                if (data.success && data.msgArray && data.msgArray.length > 0) {
                    const userId = data.msgArray[0].receiver_id;
                    Utils.log(`获取到用户ID: ${userId}`);
                    return userId;
                }

                Utils.error('API响应格式不正确或无数据', data);
                throw new Error('无法获取用户ID: API响应格式不正确');
            } catch (error) {
                Utils.error('获取用户ID失败', error);
                throw error;
            }
        }

        /**
         * 获取与指定用户的聊天消息
         * @param {number} userId 用户ID
         * @returns {Promise<Object>} 聊天消息数据
         */
        async getChatMessages(userId) {
            return HttpClient.request({
                url: `${this.baseUrl}/notification/message/with/${userId}`
            });
        }

        /**
         * 获取消息列表
         * @returns {Promise<Object>} 消息列表数据
         */
        async getMessageList() {
            return HttpClient.request({
                url: `${this.baseUrl}/notification/message/list`
            });
        }
    }

    /**
     * 配置管理器基类
     * 提供统一的配置存储和加密功能
     */
    class ConfigManager {
        /**
         * 构造函数
         * @param {number} userId 用户ID
         * @param {string} configType 配置类型
         * @param {Array<string>} sensitiveFields 需要加密的字段
         */
        constructor(userId, configType, sensitiveFields = []) {
            this.userId = userId;
            this.configKey = `${configType}_config_${userId}`;
            this.sensitiveFields = sensitiveFields;
        }

        /**
         * 获取配置
         * @returns {Object|null} 配置对象
         */
        getConfig() {
            const config = GM_getValue(this.configKey, null);
            if (!config) return null;

            try {
                const parsedConfig = JSON.parse(config);
                this.sensitiveFields.forEach(field => {
                    if (parsedConfig[field]) {
                        parsedConfig[field] = Utils.decrypt(parsedConfig[field]);
                    }
                });
                return parsedConfig;
            } catch (error) {
                Utils.error('配置解析失败', error);
                return null;
            }
        }

        /**
         * 保存配置
         * @param {Object} config 配置对象
         */
        saveConfig(config) {
            try {
                const configToSave = { ...config };
                this.sensitiveFields.forEach(field => {
                    if (configToSave[field]) {
                        configToSave[field] = Utils.encrypt(configToSave[field]);
                    }
                });
                GM_setValue(this.configKey, JSON.stringify(configToSave));
            } catch (error) {
                Utils.error('配置保存失败', error);
                throw error;
            }
        }
    }

    /**
     * AWS Signature Version 4 签名工具类
     * 用于生成 S3 API 请求的签名
     */
    class AWSSignatureV4 {
        /**
         * 构造函数
         * @param {Object} config AWS配置
         */
        constructor(config) {
            this.accessKeyId = config.accessKeyId;
            this.secretAccessKey = config.secretAccessKey;
            this.region = config.region;
            this.service = 's3';
        }

        /**
         * 计算 SHA256 哈希
         * @param {string} data 要哈希的数据
         * @returns {Promise<string>} 十六进制哈希值
         */
        async sha256(data) {
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(data);
            const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        /**
         * 计算 HMAC-SHA256
         * @param {string|Uint8Array} key 密钥
         * @param {string} data 要签名的数据
         * @returns {Promise<Uint8Array>} 签名结果
         */
        async hmacSha256(key, data) {
            const encoder = new TextEncoder();
            const keyBuffer = typeof key === 'string' ? encoder.encode(key) : key;
            const dataBuffer = encoder.encode(data);

            const cryptoKey = await crypto.subtle.importKey(
                'raw', keyBuffer, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']
            );

            const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
            return new Uint8Array(signature);
        }

        /**
         * 生成签名密钥
         * @param {string} dateStamp 日期戳
         * @returns {Promise<Uint8Array>} 签名密钥
         */
        async getSignatureKey(dateStamp) {
            const kDate = await this.hmacSha256('AWS4' + this.secretAccessKey, dateStamp);
            const kRegion = await this.hmacSha256(kDate, this.region);
            const kService = await this.hmacSha256(kRegion, this.service);
            const kSigning = await this.hmacSha256(kService, 'aws4_request');
            return kSigning;
        }

        /**
         * 生成规范化请求字符串
         * @param {string} method HTTP方法
         * @param {string} uri 请求URI
         * @param {string} queryString 查询字符串
         * @param {Object} headers 请求头
         * @param {string} payloadHash 负载哈希
         * @returns {string} 规范化请求字符串
         */
        createCanonicalRequest(method, uri, queryString, headers, payloadHash) {
            const canonicalHeaders = Object.keys(headers)
                .sort()
                .map(key => `${key.toLowerCase()}:${headers[key].trim()}\n`)
                .join('');

            const signedHeaders = Object.keys(headers)
                .sort()
                .map(key => key.toLowerCase())
                .join(';');

            return [
                method,
                uri,
                queryString,
                canonicalHeaders,
                signedHeaders,
                payloadHash
            ].join('\n');
        }

        /**
         * 生成待签名字符串
         * @param {string} timestamp 时间戳
         * @param {string} dateStamp 日期戳
         * @param {string} canonicalRequest 规范化请求
         * @returns {Promise<string>} 待签名字符串
         */
        async createStringToSign(timestamp, dateStamp, canonicalRequest) {
            const algorithm = 'AWS4-HMAC-SHA256';
            const credentialScope = `${dateStamp}/${this.region}/${this.service}/aws4_request`;
            const canonicalRequestHash = await this.sha256(canonicalRequest);

            return [
                algorithm,
                timestamp,
                credentialScope,
                canonicalRequestHash
            ].join('\n');
        }

        /**
         * 生成授权头
         * @param {string} method HTTP方法
         * @param {string} url 请求URL
         * @param {Object} headers 请求头
         * @param {string} payload 请求负载
         * @returns {Promise<Object>} 包含授权头的完整头部对象
         */
        async signRequest(method, url, headers = {}, payload = '') {
            const urlObj = new URL(url);
            const timestamp = new Date().toISOString().replace(/[:\-]|\.\d{3}/g, '');
            const dateStamp = timestamp.substring(0, 8);

            // 计算负载哈希
            const payloadHash = await this.sha256(payload);

            // 准备头部
            const signHeaders = {
                'host': urlObj.host,
                'x-amz-date': timestamp,
                'x-amz-content-sha256': payloadHash,
                ...headers
            };

            // 生成规范化请求
            const canonicalRequest = this.createCanonicalRequest(
                method,
                urlObj.pathname,
                urlObj.search.slice(1),
                signHeaders,
                payloadHash
            );

            // 生成待签名字符串
            const stringToSign = await this.createStringToSign(timestamp, dateStamp, canonicalRequest);

            // 生成签名
            const signingKey = await this.getSignatureKey(dateStamp);
            const signature = await this.hmacSha256(signingKey, stringToSign);
            const signatureHex = Array.from(signature).map(b => b.toString(16).padStart(2, '0')).join('');

            // 生成授权头
            const credentialScope = `${dateStamp}/${this.region}/${this.service}/aws4_request`;
            const signedHeaders = Object.keys(signHeaders).sort().map(key => key.toLowerCase()).join(';');

            signHeaders['authorization'] = `AWS4-HMAC-SHA256 Credential=${this.accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signatureHex}`;

            return signHeaders;
        }
    }

    /**
     * S3 备份模块
     * 使用 GM_xmlhttpRequest 绕过 CORS 限制
     */
    class S3Backup extends ConfigManager {
        /**
         * 构造函数
         * @param {number} userId 用户ID
         */
        constructor(userId) {
            super(userId, 's3', ['accessKeyId', 'secretAccessKey']);
            this.signer = null;
        }

        /**
         * 初始化签名器
         * @param {Object} config S3配置
         * @returns {AWSSignatureV4} 签名器实例
         */
        initSigner(config) {
            this.signer = new AWSSignatureV4({
                accessKeyId: config.accessKeyId,
                secretAccessKey: config.secretAccessKey,
                region: config.region
            });
            return this.signer;
        }

        /**
         * 验证和格式化S3端点URL
         * @param {string} endpoint 原始端点URL
         * @returns {string} 格式化后的端点URL
         */
        validateAndFormatEndpoint(endpoint) {
            if (!endpoint) {
                throw new Error('S3端点不能为空');
            }

            if (!endpoint.startsWith('http://') && !endpoint.startsWith('https://')) {
                endpoint = 'https://' + endpoint;
            }

            try {
                new URL(endpoint);
                return endpoint;
            } catch (error) {
                throw new Error(`S3端点URL格式不正确: ${endpoint}`);
            }
        }

        /**
         * 生成S3对象键
         * @param {Object} config S3配置
         * @param {string} filename 文件名
         * @returns {string} S3对象键
         */
        buildS3Key(config, filename) {
            return config.prefix ? `${config.prefix.replace(/\/$/, '')}/${filename}` : filename;
        }

        /**
         * 构建S3请求URL
         * @param {Object} config S3配置
         * @param {string} key 对象键
         * @param {Object} queryParams 查询参数
         * @returns {string} 完整的S3请求URL
         */
        buildS3Url(config, key = '', queryParams = {}) {
            let baseUrl;

            if (config.endpoint.includes('amazonaws.com')) {
                // AWS S3
                if (config.urlMode === 'path-style') {
                    baseUrl = `${config.endpoint}/${config.bucket}`;
                } else {
                    baseUrl = `https://${config.bucket}.s3.${config.region}.amazonaws.com`;
                }
            } else {
                // 第三方S3兼容服务
                if (config.urlMode === 'path-style') {
                    baseUrl = `${config.endpoint}/${config.bucket}`;
                } else {
                    const url = new URL(config.endpoint);
                    baseUrl = `${url.protocol}//${config.bucket}.${url.host}`;
                }
            }

            let fullUrl = key ? `${baseUrl}/${key}` : baseUrl;

            // 添加查询参数
            const queryString = Object.keys(queryParams)
                .map(k => `${encodeURIComponent(k)}=${encodeURIComponent(queryParams[k])}`)
                .join('&');

            if (queryString) {
                fullUrl += `?${queryString}`;
            }

            return fullUrl;
        }

        /**
         * 使用 GM_xmlhttpRequest 执行S3请求
         * @param {string} method HTTP方法
         * @param {string} url 请求URL
         * @param {Object} headers 请求头
         * @param {string} data 请求数据
         * @param {string} responseType 响应类型
         * @returns {Promise<*>} 响应结果
         */
        async executeS3Request(method, url, headers = {}, data = '', responseType = 'text') {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method,
                    url,
                    headers,
                    data,
                    timeout: Constants.DEFAULT_TIMEOUT,
                    responseType,
                    onload: (response) => {
                        Utils.debug(`S3请求响应: ${response.status} ${response.statusText}`);

                        if (response.status >= 200 && response.status < 300) {
                            if (responseType === 'json') {
                                try {
                                    resolve(JSON.parse(response.responseText));
                                } catch (e) {
                                    resolve(response.responseText);
                                }
                            } else {
                                resolve(response);
                            }
                        } else {
                            const error = new Error(`S3请求失败: ${response.status} ${response.statusText}`);
                            error.status = response.status;
                            error.response = response.responseText;
                            reject(error);
                        }
                    },
                    onerror: (error) => {
                        Utils.error('S3请求网络错误', error);
                        reject(new Error('S3请求网络错误，请检查网络连接'));
                    },
                    ontimeout: () => {
                        reject(new Error('S3请求超时'));
                    }
                });
            });
        }

        /**
         * 上传备份到S3
         * @param {Object} data 备份数据
         * @param {number} retryCount 重试次数
         * @returns {Promise<string>} 备份文件名
         */
        async uploadBackup(data, retryCount = 0) {
            const config = this.getConfig();
            if (!config) {
                throw new Error('S3配置未设置');
            }

            if (!config.endpoint || !config.accessKeyId || !config.secretAccessKey || !config.region || !config.bucket) {
                throw new Error('S3配置不完整，请检查所有必填项');
            }

            try {
                const filename = `nodeseek_chat_backup_${Utils.formatDate(new Date())}.json`;
                const key = this.buildS3Key(config, filename);

                Utils.debug(`S3上传: bucket=${config.bucket}, key=${key}`);

                const params = {
                    Bucket: config.bucket,
                    Key: key,
                    Body: JSON.stringify(data),
                    ContentType: 'application/json'
                };

                const response = await this.executeS3Operation('putObject', params);

                Utils.log(`S3上传成功: ${filename}`);
                Utils.debug('S3上传响应:', response);

                return filename;

            } catch (error) {
                Utils.error('S3上传失败', error);

                if (retryCount < 3 && this.shouldRetry(error)) {
                    const delay = Constants.RETRY_DELAYS[retryCount];
                    Utils.log(`S3上传失败，${delay}ms后重试 (${retryCount + 1}/3): ${error.message}`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return await this.uploadBackup(data, retryCount + 1);
                }

                throw new Error(`S3上传失败: ${error.message}`);
            }
        }

        /**
         * 判断是否应该重试
         * @param {Error} error 错误对象
         * @returns {boolean} 是否应该重试
         */
        shouldRetry(error) {
            const retryableErrors = [
                'NetworkError', 'TimeoutError', 'ServiceUnavailable',
                'InternalError', 'SlowDown'
            ];

            return retryableErrors.some(errorType =>
                error.name === errorType ||
                error.message.includes(errorType) ||
                (error.$metadata && error.$metadata.httpStatusCode >= 500)
            );
        }

        /**
         * 列出S3中的备份文件
         * @returns {Promise<Array>} 备份文件列表
         */
        async listBackups() {
            const config = this.getConfig();
            if (!config) return [];

            try {
                const prefix = config.prefix ?
                    `${config.prefix.replace(/\/$/, '')}/nodeseek_chat_backup_` :
                    'nodeseek_chat_backup_';

                Utils.debug(`S3列表: bucket=${config.bucket}, prefix=${prefix}`);

                const params = {
                    Bucket: config.bucket,
                    Prefix: prefix
                };

                const response = await this.executeS3Operation('listObjectsV2', params);

                const contents = (response.Contents || [])
                    .map(item => ({
                        key: item.Key,
                        lastModified: item.LastModified,
                        size: item.Size
                    }))
                    .filter(item => item.key && item.key.includes('nodeseek_chat_backup_'))
                    .sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));

                Utils.debug(`找到${contents.length}个S3备份文件`);
                return contents;

            } catch (error) {
                Utils.error('S3列表失败', error);
                throw new Error(`获取S3备份列表失败: ${error.message}`);
            }
        }

        /**
         * 从S3下载备份文件
         * @param {string} key S3对象键
         * @returns {Promise<Object>} 备份数据
         */
        async downloadBackup(key) {
            const config = this.getConfig();
            if (!config) {
                throw new Error('S3配置未设置');
            }

            try {
                Utils.debug(`S3下载: bucket=${config.bucket}, key=${key}`);

                const params = {
                    Bucket: config.bucket,
                    Key: key
                };

                const response = await this.executeS3Operation('getObject', params);
                const bodyText = response.Body.toString('utf-8');
                const data = JSON.parse(bodyText);

                Utils.debug('S3下载成功');
                return data;

            } catch (error) {
                Utils.error('S3下载失败', error);
                throw new Error(`下载S3备份失败: ${error.message}`);
            }
        }



        /**
         * 清理旧备份
         * @returns {Promise<void>}
         */
        async cleanOldBackups() {
            try {
                const backups = await this.listBackups();
                if (backups.length > Constants.MAX_BACKUPS) {
                    const config = this.getConfig();
                    const toDelete = backups.slice(Constants.MAX_BACKUPS);

                    Utils.log(`清理${toDelete.length}个旧备份文件`);

                    for (const backup of toDelete) {
                        try {
                            const params = {
                                Bucket: config.bucket,
                                Key: backup.key
                            };

                            await this.executeS3Operation('deleteObject', params);
                            Utils.debug(`删除旧备份成功: ${backup.key}`);

                        } catch (error) {
                            Utils.error(`删除旧备份失败: ${backup.key}`, error);
                        }
                    }
                }
            } catch (error) {
                Utils.error('清理S3旧备份失败', error);
            }
        }

        /**
         * S3连接测试
         * @returns {Promise<void>}
         */
        async testConnectionSimple() {
            const config = this.getConfig();
            if (!config) {
                throw new Error('S3配置未设置');
            }

            try {
                Utils.debug(`S3简化测试: bucket=${config.bucket}`);

                const params = {
                    Bucket: config.bucket
                };

                await this.executeS3Operation('headBucket', params);
                Utils.log('S3连接测试成功');

            } catch (error) {
                Utils.error('S3连接测试失败', error);

                const errorMessages = {
                    'NotFound': 'S3存储桶不存在，请检查存储桶名称',
                    'Forbidden': 'S3权限不足，请检查Access Key和Secret Key',
                    'NetworkingError': 'S3连接测试网络错误，请检查网络连接和端点配置'
                };

                const errorCode = error.code || (error.statusCode === 404 ? 'NotFound' :
                                 error.statusCode === 403 ? 'Forbidden' : null);

                const message = errorMessages[errorCode] ||
                               (error.message.includes('network') ? errorMessages['NetworkingError'] :
                                `S3连接测试失败: ${error.message}`);

                throw new Error(message);
            }
        }
    }

    /**
     * WebDAV 备份模块
     * 用于将聊天记录备份到WebDAV服务器
     */
    class WebDAVBackup extends ConfigManager {
        /**
         * 构造函数
         * @param {number} userId 用户ID
         */
        constructor(userId) {
            super(userId, 'webdav', ['password']);
        }



        /**
         * 执行WebDAV HTTP请求
         * @param {Object} options 请求选项
         * @returns {Promise<*>} 响应结果
         */
        async executeWebDAVRequest(options) {
            const config = this.getConfig();
            if (!config) {
                throw new Error('WebDAV配置未设置');
            }

            const {
                method,
                url,
                data,
                timeout = 10000,
                headers = {}
            } = options;

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method,
                    url,
                    headers: {
                        'Authorization': `Basic ${btoa(`${config.username}:${config.password}`)}`,
                        ...headers
                    },
                    data,
                    timeout,
                    onload: (response) => {
                        if (response.status >= 200 && response.status < 300) {
                            resolve(response);
                        } else {
                            const errorMessage = HttpClient.getErrorMessage(response.status, 'WebDAV操作');
                            reject(new Error(errorMessage));
                        }
                    },
                    onerror: (error) => {
                        Utils.error('WebDAV请求网络错误', error);
                        reject(new Error('WebDAV请求网络错误，请检查网络连接和服务器地址'));
                    },
                    ontimeout: () => {
                        reject(new Error('WebDAV请求超时，请检查网络连接和服务器地址'));
                    }
                });
            });
        }

        /**
         * 测试WebDAV连接
         * @returns {Promise<void>}
         */
        async testConnection() {
            const config = this.getConfig();
            if (!config) {
                throw new Error('WebDAV配置未设置');
            }

            try {
                await this.ensureDirectoryExists(config.backupPath);

                const testFilename = `test_connection_${Date.now()}.txt`;
                const testUrl = `${config.serverUrl.replace(/\/$/, '')}${config.backupPath.replace(/\/$/, '')}/${testFilename}`;

                Utils.debug(`WebDAV连接测试URL: ${testUrl}`);

                await this.executeWebDAVRequest({
                    method: 'PUT',
                    url: testUrl,
                    headers: { 'Content-Type': 'text/plain' },
                    data: 'WebDAV connection test'
                });

                try {
                    await this.executeWebDAVRequest({
                        method: 'DELETE',
                        url: testUrl
                    });
                } catch (deleteError) {
                    Utils.debug('删除测试文件失败，但不影响连接测试结果');
                }

            } catch (error) {
                Utils.error('WebDAV连接测试失败', error);
                throw error;
            }
        }

        /**
         * 构建完整的WebDAV URL
         * @param {string} path 文件路径
         * @returns {string} 完整的URL
         */
        buildFullUrl(path) {
            const config = this.getConfig();
            if (!config) {
                throw new Error('WebDAV配置未设置');
            }

            Utils.debug(`buildFullUrl 输入参数: path="${path}"`);
            Utils.debug(`WebDAV配置: serverUrl="${config.serverUrl}", backupPath="${config.backupPath}"`);

            if (path.startsWith('http://') || path.startsWith('https://')) {
                Utils.debug(`path是完整URL，直接返回: ${path}`);
                return path;
            }

            const serverBase = config.serverUrl.replace(/\/$/, '');
            Utils.debug(`处理后的serverBase: "${serverBase}"`);

            if (path.startsWith('/')) {
                const serverPath = new URL(serverBase).pathname;
                Utils.debug(`serverUrl的路径部分: "${serverPath}"`);

                if (path.startsWith(serverPath) && serverPath !== '/') {
                    const result = `${new URL(serverBase).origin}${path}`;
                    Utils.debug(`避免路径重复，拼接结果: ${result}`);
                    return result;
                } else {
                    const result = `${serverBase}${path}`;
                    Utils.debug(`path是绝对路径，拼接结果: ${result}`);
                    return result;
                }
            }

            const backupBase = config.backupPath.replace(/^\/+|\/+$/g, '');
            const fileName = path.replace(/^\/+/, '');

            Utils.debug(`处理后的backupBase: "${backupBase}"`);
            Utils.debug(`处理后的fileName: "${fileName}"`);

            const result = `${serverBase}/${backupBase}/${fileName}`;
            Utils.debug(`最终拼接结果: ${result}`);

            return result;
        }

        /**
         * 确保目录存在
         * @param {string} directoryPath 目录路径
         * @returns {Promise<void>}
         */
        async ensureDirectoryExists(directoryPath) {
            const config = this.getConfig();
            if (!config) {
                throw new Error('WebDAV 配置未设置');
            }

            const url = `${config.serverUrl.replace(/\/$/, '')}${directoryPath.replace(/\/$/, '')}/`;

            try {
                await this.executeWebDAVRequest({
                    method: 'PROPFIND',
                    url: url,
                    headers: { 'Depth': '0' }
                });
                Utils.log(`目录已存在: ${directoryPath}`);
            } catch (error) {
                if (error.message.includes('404') || error.message.includes('不存在')) {
                    await this.verifyAuthenticationThenCreateDirectory(directoryPath, url);
                } else {
                    throw error;
                }
            }
        }

        /**
         * 验证认证后创建目录
         * @param {string} directoryPath 目录路径
         * @param {string} url 完整URL
         * @returns {Promise<void>}
         */
        async verifyAuthenticationThenCreateDirectory(directoryPath, url) {
            const config = this.getConfig();
            const rootUrl = config.serverUrl.replace(/\/$/, '') + '/';

            try {
                await this.executeWebDAVRequest({
                    method: 'PROPFIND',
                    url: rootUrl,
                    headers: { 'Depth': '0' }
                });

                Utils.log(`认证有效，目录不存在，正在创建: ${directoryPath}`);

                await this.executeWebDAVRequest({
                    method: 'MKCOL',
                    url: url
                });

                Utils.log(`目录创建成功: ${directoryPath}`);

            } catch (error) {
                if (error.message.includes('401') || error.message.includes('认证失败')) {
                    throw new Error('WebDAV认证失败，请检查用户名和密码');
                } else if (error.message.includes('403') || error.message.includes('权限不足')) {
                    throw new Error('WebDAV权限不足，无法创建目录');
                } else {
                    throw new Error(`WebDAV服务器访问失败: ${error.message}`);
                }
            }
        }

        /**
         * 上传备份到WebDAV
         * @param {Object} data 备份数据
         * @param {number} retryCount 重试次数
         * @returns {Promise<string>} 备份文件名
         */
        async uploadBackup(data, retryCount = 0) {
            const config = this.getConfig();
            if (!config) {
                throw new Error('WebDAV 配置未设置');
            }

            try {
                await this.ensureDirectoryExists(config.backupPath);
            } catch (error) {
                throw new Error(`确保目录存在失败: ${error.message}`);
            }

            const filename = `nodeseek_chat_backup_${Utils.formatDate(new Date())}.json`;
            const url = `${config.serverUrl.replace(/\/$/, '')}${config.backupPath.replace(/\/$/, '')}/${filename}`;

            try {
                await this.executeWebDAVRequest({
                    method: 'PUT',
                    url: url,
                    headers: { 'Content-Type': 'application/json' },
                    data: JSON.stringify(data),
                    timeout: 60000
                });

                return filename;

            } catch (error) {
                if (error.message.includes('409') && retryCount < 3) {
                    const delay = Constants.RETRY_DELAYS[retryCount];
                    Utils.log(`备份冲突，${delay}ms后重试 (${retryCount + 1}/3)`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return await this.uploadBackup(data, retryCount + 1);
                }

                if (retryCount >= 3) {
                    throw new Error(`备份失败: 目录可能不存在或权限不足。请检查WebDAV配置和目录权限。`);
                }

                throw new Error(`备份上传失败: ${error.message}`);
            }
        }

        /**
         * 列出WebDAV备份文件
         * @returns {Promise<Array>} 备份文件列表
         */
        async listBackups() {
            const config = this.getConfig();
            if (!config) return [];

            const url = `${config.serverUrl.replace(/\/$/, '')}${config.backupPath.replace(/\/$/, '')}/`;

            try {
                const response = await this.executeWebDAVRequest({
                    method: 'PROPFIND',
                    url: url,
                    headers: { 'Depth': '1' }
                });

                Utils.debug(`备份列表响应: ${response.responseText.substring(0, 500)}...`);

                const parser = new DOMParser();
                const doc = parser.parseFromString(response.responseText, 'text/xml');
                const files = Array.from(doc.querySelectorAll('response'))
                    .map(response => {
                        const href = response.querySelector('href')?.textContent;
                        const lastModified = response.querySelector('getlastmodified')?.textContent;

                        Utils.debug(`找到文件: href=${href}, lastModified=${lastModified}`);

                        return { href, lastModified };
                    })
                    .filter(file => {
                        const isBackupFile = file.href && file.href.includes('nodeseek_chat_backup_');
                        Utils.debug(`文件过滤: ${file.href} -> ${isBackupFile}`);
                        return isBackupFile;
                    })
                    .sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));

                Utils.debug(`最终备份文件列表: ${files.length} 个文件`);
                return files;

            } catch (error) {
                Utils.error('获取WebDAV备份列表失败', error);
                throw new Error(`获取备份列表失败: ${error.message}`);
            }
        }

        /**
         * 清理旧备份文件
         * @returns {Promise<void>}
         */
        async cleanOldBackups() {
            try {
                const backups = await this.listBackups();
                if (backups.length > Constants.MAX_BACKUPS) {
                    const toDelete = backups.slice(Constants.MAX_BACKUPS);

                    Utils.log(`清理${toDelete.length}个旧备份文件`);

                    for (const backup of toDelete) {
                        try {
                            const deleteUrl = this.buildFullUrl(backup.href);
                            await this.executeWebDAVRequest({
                                method: 'DELETE',
                                url: deleteUrl
                            });
                            Utils.debug(`删除旧备份成功: ${backup.href}`);
                        } catch (error) {
                            Utils.error(`删除旧备份失败: ${backup.href}`, error);
                        }
                    }
                }
            } catch (error) {
                Utils.error('清理WebDAV旧备份失败', error);
            }
        }
    }

    /**
     * CSS样式常量
     */
    const UIStyles = `
        .nodeseek-modal {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .nodeseek-modal-overlay {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); display: flex; align-items: center;
            justify-content: center; padding: 20px; box-sizing: border-box;
        }
        .nodeseek-modal-content {
            background: white; border-radius: 8px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            overflow: hidden; width: 100%; display: flex; flex-direction: column;
        }
        .nodeseek-modal-header {
            padding: 16px 20px; border-bottom: 1px solid #e0e0e0; display: flex;
            justify-content: space-between; align-items: center; background: #f8f9fa;
        }
        .nodeseek-modal-header h3 { margin: 0; font-size: 18px; color: #333; }
        .nodeseek-modal-close {
            background: none; border: none; font-size: 24px; cursor: pointer; color: #666;
            padding: 0; width: 30px; height: 30px; display: flex; align-items: center;
            justify-content: center; border-radius: 4px;
        }
        .nodeseek-modal-close:hover { background: #e0e0e0; color: #333; }
        .nodeseek-modal-body { padding: 20px; overflow-y: auto; flex: 1; }
        .nodeseek-btn {
            background: #007bff; color: white; border: none; padding: 8px 16px;
            border-radius: 4px; cursor: pointer; font-size: 14px; margin: 4px;
        }
        .nodeseek-btn:hover { background: #0056b3; }
        .nodeseek-btn-secondary { background: #6c757d; }
        .nodeseek-btn-secondary:hover { background: #545b62; }
        .nodeseek-btn-success { background: #28a745; }
        .nodeseek-btn-success:hover { background: #1e7e34; }
        .nodeseek-form-group { margin-bottom: 16px; }
        .nodeseek-form-group label {
            display: block; margin-bottom: 4px; font-weight: 500; color: #333;
        }
        .nodeseek-form-group input, .nodeseek-form-group textarea, .nodeseek-form-group select {
            width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;
            font-size: 14px; box-sizing: border-box;
        }
        .nodeseek-form-group input:focus, .nodeseek-form-group textarea:focus, .nodeseek-form-group select:focus {
            outline: none; border-color: #007bff; box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
        .nodeseek-chat-item {
            display: flex; align-items: center; padding: 12px; border-bottom: 1px solid #e0e0e0;
            transition: background 0.2s;
        }
        .nodeseek-chat-item:hover { background: #f8f9fa; }
        .nodeseek-chat-avatar {
            width: 40px; height: 40px; border-radius: 50%; margin-right: 12px; object-fit: cover;
        }
        .nodeseek-chat-info { flex: 1; min-width: 0; }
        .nodeseek-chat-name { font-weight: 500; color: #333; margin-bottom: 4px; }
        .nodeseek-chat-message {
            color: #666; font-size: 14px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
        }
        .nodeseek-chat-time { color: #999; font-size: 12px; margin-left: 12px; white-space: nowrap; }
        .nodeseek-chat-actions { margin-left: 12px; }
        .nodeseek-history-btn {
            display: inline-block; background: #007bff; color: white; border: none;
            padding: 8px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;
            margin-left: 8px; text-decoration: none; vertical-align: middle;
            transition: all 0.2s; line-height: 1.2;
        }
        .nodeseek-history-btn:hover { background: #0056b3; color: white; text-decoration: none; }
    `;

    /**
     * UI 管理模块
     * 负责用户界面的创建和管理
     */
    class UIManager {
        /**
         * 构造函数
         */
        constructor() {
            this.modals = new Set();
            this.stylesLoaded = false;
            this.talkListObserver = null;
            this.lastTalkListPresent = false;
            // 通知队列
            this.toastQueue = [];
            this.isShowingToast = false;
        }

        /**
         * 检测私信页面出现/消失的回调
         * @param {boolean} isPresent 私信页面是否存在
         */
        onMessagePageChange(isPresent) {
            if (isPresent) {
                Utils.debug('私信页面出现了');
                this.addHistoryButton();
            } else {
                Utils.debug('私信页面消失了');
                this.removeHistoryButton();
            }
        }

        /**
         * 检查私信页面状态
         */
        checkMessagePage() {
            const appSwitch = document.querySelector('.app-switch');
            const messageLink = appSwitch?.querySelector('a[href="#/message?mode=list"]');
            const isMessagePage = messageLink?.classList.contains('router-link-active');

            if (isMessagePage !== this.lastTalkListPresent) {
                this.lastTalkListPresent = isMessagePage;
                this.onMessagePageChange(isMessagePage);
            }
        }

        /**
         * 初始化私信页面监听器
         */
        initTalkListObserver() {
            if (this.talkListObserver) {
                this.talkListObserver.disconnect();
            }

            this.talkListObserver = new MutationObserver(() => {
                this.checkMessagePage();
            });

            this.talkListObserver.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class']
            });

            this.checkMessagePage();
        }

        /**
         * 确保样式已加载
         */
        ensureStylesLoaded() {
            if (this.stylesLoaded || document.querySelector('#nodeseek-modal-styles')) {
                this.stylesLoaded = true;
                return;
            }

            const styles = document.createElement('style');
            styles.id = 'nodeseek-modal-styles';
            styles.textContent = UIStyles;
            document.head.appendChild(styles);
            this.stylesLoaded = true;
        }

        /**
         * 创建模态框
         * @param {string} title 模态框标题
         * @param {string} content 模态框内容HTML
         * @param {Object} options 选项配置
         * @returns {HTMLElement} 模态框元素
         */
        createModal(title, content, options = {}) {
            this.ensureStylesLoaded();

            const modal = document.createElement('div');
            modal.className = 'nodeseek-modal';
            modal.innerHTML = `
                <div class="nodeseek-modal-overlay">
                    <div class="nodeseek-modal-content" style="max-width: ${options.width || '600px'}; max-height: ${options.height || '80vh'};">
                        <div class="nodeseek-modal-header">
                            <h3>${title}</h3>
                            <button class="nodeseek-modal-close">&times;</button>
                        </div>
                        <div class="nodeseek-modal-body">
                            ${content}
                        </div>
                    </div>
                </div>
            `;

            const closeModal = () => {
                modal.remove();
                this.modals.delete(modal);
            };

            modal.querySelector('.nodeseek-modal-close').addEventListener('click', closeModal);
            modal.querySelector('.nodeseek-modal-overlay').addEventListener('click', (e) => {
                if (e.target === e.currentTarget) closeModal();
            });

            document.body.appendChild(modal);
            this.modals.add(modal);

            return modal;
        }

        /**
         * 显示备份配置对话框
         * @param {Object} backupInstances - 备份实例对象 {webdav, s3}
         * @param {Function} onSave - 保存回调函数
         */
        showBackupConfig(backupInstances, onSave) {
            // 获取当前备份模式设置，首次使用默认为webdav
            const currentBackupMode = GM_getValue(`backup_mode_${backupInstances.webdav.userId}`, 'webdav');
            // 获取默认恢复源设置
            const defaultRestoreSource = GM_getValue(`default_restore_source_${backupInstances.webdav.userId}`, 'webdav');

            const content = `
                <div class="nodeseek-form-group">
                    <label>备份方式</label>
                    <select id="backup-mode" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        <option value="webdav" ${currentBackupMode === 'webdav' ? 'selected' : ''}>仅WebDAV</option>
                        <option value="s3" ${currentBackupMode === 's3' ? 'selected' : ''}>仅S3对象存储</option>
                        <option value="both" ${currentBackupMode === 'both' ? 'selected' : ''}>WebDAV + S3 双重备份</option>
                    </select>
                </div>

                <div class="nodeseek-form-group" id="restore-source-group" style="display: ${currentBackupMode === 'both' ? 'block' : 'none'};">
                    <label>默认恢复数据源</label>
                    <select id="default-restore-source" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        <option value="webdav" ${defaultRestoreSource === 'webdav' ? 'selected' : ''}>WebDAV</option>
                        <option value="s3" ${defaultRestoreSource === 's3' ? 'selected' : ''}>S3对象存储</option>
                    </select>
                </div>

                <div style="margin: 20px 0;">
                    <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 12px;">
                        <button class="nodeseek-btn" id="export-data-btn" style="background: #17a2b8;">导出数据</button>
                        <button class="nodeseek-btn" id="import-data-btn" style="background: #6f42c1;">导入数据</button>
                        <button class="nodeseek-btn" id="test-webdav-btn" style="background: #28a745; display: none;">测试WebDAV连接</button>
                        <button class="nodeseek-btn" id="test-s3-btn" style="background: #fd7e14; display: none;">测试S3连接</button>

                        <button class="nodeseek-btn nodeseek-btn-success" id="test-save-btn" style="display: none;">保存</button>
                        <input type="file" id="import-file" accept=".json" style="display: none;">
                    </div>

                    <div style="padding: 12px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <strong>调试模式</strong>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">
                                    开启后会在浏览器控制台显示详细的调试信息，用于排查问题
                                </div>
                            </div>
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="checkbox" id="debug-toggle" style="margin-right: 8px;">
                                <span id="debug-status">关闭</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div id="webdav-config">
                    <h4 style="margin: 16px 0 8px 0; color: #333;">WebDAV 配置</h4>
                    <div class="nodeseek-form-group">
                        <label>服务器地址</label>
                        <input type="url" id="webdav-server" placeholder="https://dav.jianguoyun.com/dav/">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>用户名</label>
                        <input type="text" id="webdav-username" placeholder="用户名">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>密码</label>
                        <input type="password" id="webdav-password" placeholder="密码">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>备份路径</label>
                        <input type="text" id="webdav-path" placeholder="/nodeseek_messages_backup/">
                    </div>
                </div>

                <div id="s3-config">
                    <h4 style="margin: 16px 0 8px 0; color: #333;">S3 配置</h4>
                    <div class="nodeseek-form-group">
                        <label>URL 模式</label>
                        <select id="s3-url-mode" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="path-style">路径风格 (https://s3.example.com/bucket/key)</option>
                            <option value="virtual-hosted">虚拟主机风格 (https://bucket.s3.example.com/key)</option>
                        </select>
                        <div style="font-size: 11px; color: #666; margin-top: 4px;">
                            路径风格适用于大多数S3兼容服务，虚拟主机风格适用于AWS S3等
                        </div>
                    </div>
                    <div class="nodeseek-form-group">
                        <label>S3 端点 (Endpoint)</label>
                        <input type="url" id="s3-endpoint" placeholder="https://s3.amazonaws.com">
                        <div style="font-size: 11px; color: #666; margin-top: 4px;">
                            示例：AWS S3: https://s3.amazonaws.com | 阿里云: https://oss-cn-hangzhou.aliyuncs.com | 腾讯云: https://cos.ap-beijing.myqcloud.com
                        </div>
                    </div>
                    <div class="nodeseek-form-group">
                        <label>Access Key ID</label>
                        <input type="text" id="s3-access-key" placeholder="AKIA...">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>Secret Access Key</label>
                        <input type="password" id="s3-secret-key" placeholder="密钥">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>区域 (Region)</label>
                        <input type="text" id="s3-region" placeholder="us-east-1，某些服务商可能是 auto">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>存储桶 (Bucket)</label>
                        <input type="text" id="s3-bucket" placeholder="my-backup-bucket">
                    </div>
                    <div class="nodeseek-form-group">
                        <label>路径前缀 (可选)</label>
                        <input type="text" id="s3-prefix" placeholder="nodeseek-backups/">
                    </div>
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button class="nodeseek-btn nodeseek-btn-secondary" id="backup-cancel">取消</button>
                    <button class="nodeseek-btn nodeseek-btn-success" id="backup-save">保存</button>
                </div>
            `;

            const modal = this.createModal('备份配置', content, { width: '500px' });

            // 加载现有配置
            this.loadBackupConfig(backupInstances, modal);

            // 初始化debug状态
            this.initDebugToggle(modal);

            // 备份模式切换事件
            modal.querySelector('#backup-mode').addEventListener('change', (e) => {
                const restoreSourceGroup = modal.querySelector('#restore-source-group');
                const testWebdavBtn = modal.querySelector('#test-webdav-btn');
                const testS3Btn = modal.querySelector('#test-s3-btn');

                const testSaveBtn = modal.querySelector('#test-save-btn');

                if (e.target.value === 'both') {
                    restoreSourceGroup.style.display = 'block';
                    testWebdavBtn.style.display = 'inline-block';
                    testS3Btn.style.display = 'inline-block';

                    testSaveBtn.style.display = 'inline-block';
                } else if (e.target.value === 'webdav') {
                    restoreSourceGroup.style.display = 'none';
                    testWebdavBtn.style.display = 'inline-block';
                    testS3Btn.style.display = 'none';

                    testSaveBtn.style.display = 'inline-block';
                } else if (e.target.value === 's3') {
                    restoreSourceGroup.style.display = 'none';
                    testWebdavBtn.style.display = 'none';
                    testS3Btn.style.display = 'inline-block';

                    testSaveBtn.style.display = 'inline-block';
                } else {
                    restoreSourceGroup.style.display = 'none';
                    testWebdavBtn.style.display = 'none';
                    testS3Btn.style.display = 'none';

                    testSaveBtn.style.display = 'none';
                }
            });

            // 初始化按钮显示状态
            const initialMode = modal.querySelector('#backup-mode').value;
            const testWebdavBtn = modal.querySelector('#test-webdav-btn');
            const testS3Btn = modal.querySelector('#test-s3-btn');

            const testSaveBtn = modal.querySelector('#test-save-btn');

            if (initialMode === 'both') {
                testWebdavBtn.style.display = 'inline-block';
                testS3Btn.style.display = 'inline-block';

                testSaveBtn.style.display = 'inline-block';
            } else if (initialMode === 'webdav') {
                testWebdavBtn.style.display = 'inline-block';
                testS3Btn.style.display = 'none';

                testSaveBtn.style.display = 'inline-block';
            } else if (initialMode === 's3') {
                testWebdavBtn.style.display = 'none';
                testS3Btn.style.display = 'inline-block';

                testSaveBtn.style.display = 'inline-block';
            }

            // 导出数据事件
            modal.querySelector('#export-data-btn').addEventListener('click', async () => {
                try {
                    // 需要从ChatBackup实例获取数据库，而不是从backupInstances
                    let allChats = [];
                    if (window.chatBackup && window.chatBackup.db) {
                        allChats = await window.chatBackup.db.getAllTalkMessages();
                    }
                    Utils.debug(`导出数据：找到 ${allChats.length} 条聊天记录`);
                    await this.exportDataToLocal(backupInstances, allChats);
                } catch (error) {
                    Utils.error('导出数据失败', error);
                    this.showToast('导出失败: ' + error.message, 'error');
                }
            });

            // 导入数据事件
            modal.querySelector('#import-data-btn').addEventListener('click', () => {
                modal.querySelector('#import-file').click();
            });

            modal.querySelector('#import-file').addEventListener('change', async (e) => {
                const file = e.target.files[0];
                if (file) {
                    try {
                        await this.importDataFromLocal(file, backupInstances);
                        e.target.value = ''; // 清空文件选择
                    } catch (error) {
                        this.showToast('导入失败: ' + error.message, 'error');
                    }
                }
            });

            // WebDAV连接测试事件
            modal.querySelector('#test-webdav-btn').addEventListener('click', async () => {
                try {
                    this.showToast('正在测试WebDAV连接...', 'info');
                    await this.testWebDAVConnection(backupInstances.webdav);
                } catch (error) {
                    Utils.error('WebDAV连接测试失败', error);
                    this.showToast('WebDAV连接测试失败: ' + error.message, 'error');
                }
            });

            // S3连接测试事件
            modal.querySelector('#test-s3-btn').addEventListener('click', async () => {
                try {
                    this.showToast('正在测试S3连接...', 'info');
                    await window.chatBackup.testS3Connection(backupInstances.s3);
                    this.showToast('✅ S3连接测试成功！', 'success');
                } catch (error) {
                    Utils.error('S3连接测试失败', error);
                    this.showToast('S3连接测试失败: ' + error.message, 'error');
                }
            });



            // 测试按钮后的保存事件
            modal.querySelector('#test-save-btn').addEventListener('click', () => {
                this.saveBackupConfig(backupInstances, modal, onSave);
            });

            modal.querySelector('#backup-cancel').addEventListener('click', () => modal.remove());
            modal.querySelector('#backup-save').addEventListener('click', () => {
                this.saveBackupConfig(backupInstances, modal, onSave);
                // 保存后不再自动关闭面板，让用户手动关闭
            });
        }

        /**
         * 加载备份配置到表单
         * @param {Object} backupInstances - 备份实例对象
         * @param {HTMLElement} modal - 模态框元素
         */
        loadBackupConfig(backupInstances, modal) {
            // 加载WebDAV配置
            const webdavConfig = backupInstances.webdav.getConfig() || {};
            modal.querySelector('#webdav-server').value = webdavConfig.serverUrl || '';
            modal.querySelector('#webdav-username').value = webdavConfig.username || '';
            modal.querySelector('#webdav-password').value = webdavConfig.password || '';
            modal.querySelector('#webdav-path').value = webdavConfig.backupPath || '/nodeseek_messages_backup/';

            // 加载S3配置
            const s3Config = backupInstances.s3.getConfig() || {};
            modal.querySelector('#s3-url-mode').value = s3Config.urlMode || 'path-style';
            modal.querySelector('#s3-endpoint').value = s3Config.endpoint || '';
            modal.querySelector('#s3-access-key').value = s3Config.accessKeyId || '';
            modal.querySelector('#s3-secret-key').value = s3Config.secretAccessKey || '';
            modal.querySelector('#s3-region').value = s3Config.region || '';
            modal.querySelector('#s3-bucket').value = s3Config.bucket || '';
            modal.querySelector('#s3-prefix').value = s3Config.prefix || 'nodeseek_messages_backup/';
        }

        /**
         * 初始化debug切换功能
         * @param {HTMLElement} modal - 模态框元素
         */
        initDebugToggle(modal) {
            const debugToggle = modal.querySelector('#debug-toggle');
            const debugStatus = modal.querySelector('#debug-status');

            // 设置当前debug状态
            debugToggle.checked = Utils.DEBUG;
            debugStatus.textContent = Utils.DEBUG ? '开启' : '关闭';
            debugStatus.style.color = Utils.DEBUG ? '#28a745' : '#6c757d';

            // 添加切换事件
            debugToggle.addEventListener('change', (e) => {
                const isEnabled = e.target.checked;

                // 使用Utils的setDebugMode方法
                Utils.setDebugMode(isEnabled);

                // 更新状态显示
                debugStatus.textContent = isEnabled ? '开启' : '关闭';
                debugStatus.style.color = isEnabled ? '#28a745' : '#6c757d';

                // 显示提示
                this.showToast(
                    isEnabled ? '调试模式已开启，请查看浏览器控制台' : '调试模式已关闭',
                    isEnabled ? 'success' : 'info'
                );

                // 如果开启debug，输出一条测试日志
                if (isEnabled) {
                    Utils.debug('调试模式已开启');
                    Utils.log('您可以在浏览器控制台查看详细的调试信息');
                }
            });
        }

        /**
         * 保存备份配置
         * @param {Object} backupInstances - 备份实例对象
         * @param {HTMLElement} modal - 模态框元素
         * @param {Function} onSave - 保存回调函数
         */
        saveBackupConfig(backupInstances, modal, onSave) {
            const backupMode = modal.querySelector('#backup-mode').value;

            // 根据备份模式验证配置
            if (backupMode === 'webdav' || backupMode === 'both') {
                const webdavConfig = {
                    serverUrl: modal.querySelector('#webdav-server').value.trim(),
                    username: modal.querySelector('#webdav-username').value.trim(),
                    password: modal.querySelector('#webdav-password').value.trim(),
                    backupPath: modal.querySelector('#webdav-path').value.trim()
                };

                if (!webdavConfig.serverUrl || !webdavConfig.username || !webdavConfig.password) {
                    alert('请填写完整的WebDAV配置信息');
                    return;
                }

                backupInstances.webdav.saveConfig(webdavConfig);
            }

            if (backupMode === 's3' || backupMode === 'both') {
                const s3Config = {
                    urlMode: modal.querySelector('#s3-url-mode').value,
                    endpoint: modal.querySelector('#s3-endpoint').value.trim(),
                    accessKeyId: modal.querySelector('#s3-access-key').value.trim(),
                    secretAccessKey: modal.querySelector('#s3-secret-key').value.trim(),
                    region: modal.querySelector('#s3-region').value.trim(),
                    bucket: modal.querySelector('#s3-bucket').value.trim(),
                    prefix: modal.querySelector('#s3-prefix').value.trim()
                };

                if (!s3Config.endpoint || !s3Config.accessKeyId || !s3Config.secretAccessKey || !s3Config.region || !s3Config.bucket) {
                    alert('请填写完整的S3配置信息\n\n必填项：\n- S3端点\n- Access Key ID\n- Secret Access Key\n- 区域\n- 存储桶');
                    return;
                }

                // 验证端点URL格式
                try {
                    if (!s3Config.endpoint.startsWith('http://') && !s3Config.endpoint.startsWith('https://')) {
                        s3Config.endpoint = 'https://' + s3Config.endpoint;
                    }
                    new URL(s3Config.endpoint);
                } catch (error) {
                    alert('S3端点URL格式不正确\n\n示例：\n- https://s3.amazonaws.com\n- https://oss-cn-hangzhou.aliyuncs.com\n- https://cos.ap-beijing.myqcloud.com');
                    return;
                }

                backupInstances.s3.saveConfig(s3Config);
            }

            // 保存备份模式选择
            GM_setValue(`backup_mode_${backupInstances.webdav.userId}`, backupMode);

            // 保存默认恢复源选择
            if (backupMode === 'both') {
                const defaultRestoreSource = modal.querySelector('#default-restore-source').value;
                GM_setValue(`default_restore_source_${backupInstances.webdav.userId}`, defaultRestoreSource);
            }

            if (onSave) onSave(backupMode);
        }

        /**
         * 导出数据到本地
         * @param {Object} backupInstances - 备份实例对象
         * @param {Array} chatData - 聊天数据
         */
        async exportDataToLocal(backupInstances, chatData) {
            try {
                // 获取配置信息
                const webdavConfig = backupInstances.webdav.getConfig();
                const s3Config = backupInstances.s3.getConfig();
                const backupMode = GM_getValue(`backup_mode_${backupInstances.webdav.userId}`, 'webdav');
                const defaultRestoreSource = GM_getValue(`default_restore_source_${backupInstances.webdav.userId}`, 'webdav');

                const exportData = {
                    metadata: {
                        exportTime: new Date().toISOString(),
                        userId: backupInstances.webdav.userId,
                        totalChats: chatData.length,
                        version: '1.1.0'
                    },
                    chats: chatData,
                    config: {
                        backupMode: backupMode,
                        defaultRestoreSource: defaultRestoreSource,
                        webdav: webdavConfig,
                        s3: s3Config
                    }
                };

                // 创建下载链接
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `nodeseek_export_${Utils.formatDate(new Date())}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                this.showToast('数据导出成功');
            } catch (error) {
                Utils.error('导出数据失败', error);
                this.showToast('导出数据失败: ' + error.message, 'error');
            }
        }

        /**
         * 从本地导入数据
         * @param {File} file - 导入的文件
         * @param {Object} backupInstances - 备份实例对象
         */
        async importDataFromLocal(file, backupInstances) {
            try {
                const text = await file.text();
                const importData = JSON.parse(text);

                // 验证数据格式
                if (!importData.metadata || !importData.chats || !Array.isArray(importData.chats)) {
                    throw new Error('导入文件格式不正确');
                }

                // 确认导入
                const confirmMessage = `确定要导入数据吗？\n\n` +
                    `文件信息：\n` +
                    `- 导出时间：${new Date(importData.metadata.exportTime).toLocaleString()}\n` +
                    `- 聊天记录数：${importData.chats.length}\n` +
                    `- 版本：${importData.metadata.version || '未知'}\n\n` +
                    `⚠️ 警告：此操作会覆盖现有的本地数据和配置！`;

                if (!confirm(confirmMessage)) {
                    return;
                }

                // 显示导入进度
                this.showToast('正在导入数据，请稍候...', 'info', 10000);

                // 导入聊天数据
                if (backupInstances.webdav.db) {
                    // 清空现有数据
                    await this.clearAllChatData(backupInstances.webdav.db);

                    // 导入新数据
                    let successCount = 0;
                    for (const chat of importData.chats) {
                        try {
                            await backupInstances.webdav.db.saveTalkMessage(chat);
                            successCount++;
                        } catch (dbError) {
                            Utils.error(`保存聊天记录失败 (ID: ${chat.member_id})`, dbError);
                        }
                    }

                    Utils.log(`导入完成，共导入 ${successCount} 条聊天记录`);
                }

                // 导入配置（如果存在）
                if (importData.config) {
                    if (importData.config.backupMode) {
                        GM_setValue(`backup_mode_${backupInstances.webdav.userId}`, importData.config.backupMode);
                    }
                    if (importData.config.defaultRestoreSource) {
                        GM_setValue(`default_restore_source_${backupInstances.webdav.userId}`, importData.config.defaultRestoreSource);
                    }
                    if (importData.config.webdav) {
                        backupInstances.webdav.saveConfig(importData.config.webdav);
                    }
                    if (importData.config.s3) {
                        backupInstances.s3.saveConfig(importData.config.s3);
                    }
                }

                this.showToast(`导入完成，共导入 ${importData.chats.length} 条聊天记录，3秒后自动刷新页面`, 'success', 3000);

                // 3秒后自动刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 3000);

            } catch (error) {
                Utils.error('导入数据失败', error);
                throw error;
            }
        }

        /**
         * 清空所有聊天数据
         * @param {ChatDB} db - 数据库实例
         * @returns {Promise<void>}
         */
        async clearAllChatData(db) {
            try {
                const transaction = db.db.transaction(['talk_messages'], 'readwrite');
                const store = transaction.objectStore('talk_messages');

                return new Promise((resolve, reject) => {
                    const request = store.clear();
                    request.onsuccess = () => {
                        Utils.debug('所有聊天记录已清空');
                        resolve();
                    };
                    request.onerror = () => reject(request.error);
                });
            } catch (error) {
                Utils.error('清空聊天数据失败', error);
                throw error;
            }
        }

        /**
         * 显示历史聊天记录
         * @param {Array} chatData - 聊天数据数组
         * @param {boolean} showLatest - 是否显示最新聊天，默认false
         * @returns {HTMLElement} 模态框元素
         */
        showHistoryChats(chatData, showLatest = false) {
            const sortedChats = chatData
                .filter(chat => showLatest || !chat.isLatest)
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            let content = `
                <div style="margin-bottom: 16px; display: flex; gap: 8px; flex-wrap: wrap;">
                    <button class="nodeseek-btn" id="backup-config-btn">备份设置</button>
                    <button class="nodeseek-btn nodeseek-btn-success" id="backup-now-btn">立即备份</button>
                    <button class="nodeseek-btn nodeseek-btn-secondary" id="restore-btn">恢复备份</button>
                    <label style="display: flex; align-items: center; margin-left: auto;">
                        <input type="checkbox" id="show-latest-toggle" ${showLatest ? 'checked' : ''} style="margin-right: 4px;">
                        显示最新聊天
                    </label>
                </div>
                <div style="max-height: 400px; overflow-y: auto;">
            `;

            if (sortedChats.length === 0) {
                content += '<div style="text-align: center; color: #666; padding: 40px;">暂无历史聊天记录</div>';
            } else {
                sortedChats.forEach(chat => {
                    const avatarUrl = `https://www.nodeseek.com/avatar/${chat.member_id}.png`;
                    const chatUrl = `https://www.nodeseek.com/notification#/message?mode=talk&to=${chat.member_id}`;
                    const timeStr = Utils.parseUTCToLocal(chat.created_at);

                    content += `
                        <div class="nodeseek-chat-item">
                            <img class="nodeseek-chat-avatar" src="${avatarUrl}" alt="${chat.member_name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNlMGUwZTAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOTk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5OTkiLz4KPC9zdmc+Cjwvc3ZnPgo='">
                            <div class="nodeseek-chat-info">
                                <div class="nodeseek-chat-name">${chat.member_name} (ID: ${chat.member_id})</div>
                                <div class="nodeseek-chat-message">${chat.content.replace(/<[^>]*>/g, '').substring(0, 50)}${chat.content.length > 50 ? '...' : ''}</div>
                            </div>
                            <div class="nodeseek-chat-time">${timeStr}</div>
                            <div class="nodeseek-chat-actions">
                                <a href="${chatUrl}" target="_blank" class="nodeseek-btn" style="text-decoration: none; font-size: 12px; padding: 4px 8px;">打开对话</a>
                            </div>
                        </div>
                    `;
                });
            }

            content += '</div>';

            return this.createModal('历史聊天记录', content, { width: '800px', height: '600px' });
        }

        /**
         * 添加历史聊天按钮
         */
        addHistoryButton() {
            this.ensureStylesLoaded();

            const existingBtn = document.querySelector('.nodeseek-history-btn');
            if (existingBtn) existingBtn.remove();

            const appSwitch = document.querySelector('.app-switch');
            const messageLink = appSwitch?.querySelector('a[href="#/message?mode=list"]');

            if (!appSwitch || !messageLink) {
                Utils.debug('app-switch 或私信链接元素不存在，无法添加按钮');
                return;
            }

            const btn = document.createElement('a');
            btn.className = 'nodeseek-history-btn';
            btn.textContent = '历史私信';
            btn.href = 'javascript:void(0)';
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                window.chatBackup?.showHistoryChats();
            });

            // 将按钮插入到私信链接后面
            messageLink.insertAdjacentElement('afterend', btn);
            Utils.debug('历史聊天按钮已添加到私信链接后面');
        }

        /**
         * 移除历史聊天按钮
         */
        removeHistoryButton() {
            const btn = document.querySelector('.nodeseek-history-btn');
            if (btn) btn.remove();
        }

        /**
         * 测试WebDAV连接
         * @param {WebDAVBackup} webdavInstance - WebDAV备份实例
         * @returns {Promise<void>}
         */
        async testWebDAVConnection(webdavInstance) {
            try {
                await webdavInstance.testConnection();
                this.showToast('✅ WebDAV连接测试成功！', 'success');
            } catch (error) {
                Utils.error('WebDAV连接测试失败', error);
                throw error;
            }
        }

        /**
         * 显示提示消息
         * @param {string} message - 提示消息内容
         * @param {string} type - 消息类型：'success', 'error', 'warning', 'info'
         * @param {number} duration - 显示持续时间（毫秒），默认3000
         */
        showToast(message, type = 'success', duration = 3000) {
            // 将通知加入队列
            this.toastQueue.push({ message, type, duration });

            // 如果当前没有显示通知，开始处理队列
            if (!this.isShowingToast) {
                this.processToastQueue();
            }
        }

        /**
         * 处理通知队列
         */
        processToastQueue() {
            if (this.toastQueue.length === 0) {
                this.isShowingToast = false;
                return;
            }

            this.isShowingToast = true;
            const toastData = this.toastQueue.shift();
            this.displayToast(toastData.message, toastData.type, toastData.duration);
        }

        /**
         * 实际显示通知
         * @param {string} message - 提示消息内容
         * @param {string} type - 消息类型
         * @param {number} duration - 显示持续时间
         */
        displayToast(message, type, duration) {
            const toast = document.createElement('div');
            toast.className = 'nodeseek-toast';

            const bgColor = type === 'success' ? '#28a745' :
                           type === 'error' ? '#dc3545' :
                           type === 'warning' ? '#ffc107' : '#007bff';

            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${bgColor};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10001;
                font-size: 14px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                max-width: 300px;
                word-wrap: break-word;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;

            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 隐藏动画和移除
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                    // 当前通知完成，处理队列中的下一个
                    this.processToastQueue();
                }, 300);
            }, duration);
        }
    }

    /**
     * 主控制模块
     */
    class ChatBackup {
        /**
         * 构造函数
         */
        constructor() {
            this.api = new NodeSeekAPI();
            this.db = null;
            this.webdav = null;
            this.s3 = null;
            this.ui = new UIManager();
            this.userId = null;
            this.backupTimer = null;
            this.lastHash = '';
            this.showLatestChats = GM_getValue('show_latest_chats', true);
        }

        /**
         * 初始化应用
         * @returns {Promise<void>}
         */
        async init() {
            try {
                Utils.debug('开始初始化脚本...');

                // 检查是否在正确的域名
                if (window.location.hostname !== 'www.nodeseek.com') {
                    Utils.debug('不在NodeSeek域名，跳过初始化');
                    return;
                }

                // 获取用户ID
                this.userId = await this.api.getUserId();

                // 初始化数据库和备份模块
                this.db = new ChatDB(this.userId);
                await this.db.init();

                this.webdav = new WebDAVBackup(this.userId);
                this.s3 = new S3Backup(this.userId);

                this.setupPageListener();

                this.registerMenuCommands();

                this.handlePageChange();

                this.ui.initTalkListObserver();

                Utils.log('NodeSeek私信优化脚本初始化完成');
            } catch (error) {
                Utils.error('初始化失败', error);
                if (error.message.includes('用户未登录')) {
                    console.warn('[NodeSeek私信优化] 请先登录NodeSeek账户');
                } else if (error.message.includes('响应解析失败')) {
                    console.warn('[NodeSeek私信优化] 网络请求失败，请检查网络连接或稍后重试');
                } else {
                    console.warn('[NodeSeek私信优化] 初始化失败，请刷新页面重试');
                }
            }
        }



        /**
         * 设置页面监听器
         */
        setupPageListener() {
            window.addEventListener('hashchange', () => {
                this.handlePageChange();
            });

            const observer = new MutationObserver(() => {
                if (window.location.hash !== this.lastHash) {
                    this.lastHash = window.location.hash;
                    this.handlePageChange();
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        /**
         * 处理页面变化
         * @returns {Promise<void>}
         */
        async handlePageChange() {
            const hash = window.location.hash;
            this.lastHash = hash;

            try {
                if (hash.includes('mode=talk&to=')) {
                    const match = hash.match(/to=(\d+)/);
                    if (match) {
                        const targetUserId = parseInt(match[1]);
                        await this.handleChatPage(targetUserId);
                    }
                } else if (hash.includes('mode=list')) {
                    await this.handleMessageListPage();
                }
            } catch (error) {
                Utils.error('页面处理失败', error);
            }
        }

        /**
         * 处理聊天页面
         * @param {number} targetUserId - 目标用户ID
         * @returns {Promise<void>}
         */
        async handleChatPage(targetUserId) {
            try {
                const response = await this.api.getChatMessages(targetUserId);
                if (response.success && response.msgArray && response.msgArray.length > 0) {
                    const latestMessage = response.msgArray[response.msgArray.length - 1];
                    const talkTo = response.talkTo;

                    const chatData = {
                        member_id: talkTo.member_id,
                        member_name: talkTo.member_name,
                        content: latestMessage.content,
                        created_at: latestMessage.created_at,
                        sender_id: latestMessage.sender_id,
                        receiver_id: latestMessage.receiver_id,
                        message_id: latestMessage.id,
                        viewed: latestMessage.viewed,
                        updated_at: new Date().toISOString()
                    };

                    const existingData = await this.db.getTalkMessage(talkTo.member_id);
                    if (!existingData || existingData.created_at !== latestMessage.created_at) {
                        await this.db.saveTalkMessage(chatData);
                        Utils.log(`更新聊天记录: ${talkTo.member_name}`);
                        this.performBackup();
                    }
                }
            } catch (error) {
                if (error.message === '用户未登录') {
                    Utils.log('用户未登录，停止操作');
                    return;
                }
                Utils.error('处理聊天页面失败', error);
            }
        }

        /**
         * 处理消息列表页面
         * @returns {Promise<void>}
         */
        async handleMessageListPage() {
            try {
                const response = await this.api.getMessageList();
                if (response.success && response.msgArray) {
                    let hasUpdates = false;
                    const currentChatUserIds = new Set();

                    for (const msg of response.msgArray) {
                        let chatUserId, chatUserName;
                        if (msg.sender_id === this.userId) {
                            chatUserId = msg.receiver_id;
                            chatUserName = msg.receiver_name;
                        } else {
                            chatUserId = msg.sender_id;
                            chatUserName = msg.sender_name;
                        }

                        currentChatUserIds.add(chatUserId);

                        const chatData = {
                            member_id: chatUserId,
                            member_name: chatUserName,
                            content: msg.content,
                            created_at: msg.created_at,
                            sender_id: msg.sender_id,
                            receiver_id: msg.receiver_id,
                            message_id: msg.max_id,
                            viewed: msg.viewed,
                            updated_at: new Date().toISOString(),
                            isLatest: true
                        };

                        const existingData = await this.db.getTalkMessage(chatUserId);
                        if (!existingData || existingData.created_at !== msg.created_at) {
                            await this.db.saveTalkMessage(chatData);
                            hasUpdates = true;
                        }
                    }

                    const allChats = await this.db.getAllTalkMessages();
                    for (const chat of allChats) {
                        if (!currentChatUserIds.has(chat.member_id) && chat.isLatest) {
                            chat.isLatest = false;
                            await this.db.saveTalkMessage(chat);
                        }
                    }

                    if (hasUpdates) {
                        this.performBackup();
                    }
                }
            } catch (error) {
                if (error.message === '用户未登录') {
                    Utils.log('用户未登录，停止操作');
                    return;
                }
                Utils.error('处理消息列表页面失败', error);
            }
        }

        /**
         * 获取当前备份模式
         * @returns {string} 备份模式 ('webdav', 's3', 或 'both')
         */
        getCurrentBackupMode() {
            return GM_getValue(`backup_mode_${this.userId}`, 'webdav');
        }

        /**
         * 执行备份操作
         * @returns {Promise<void>}
         */
        async performBackup() {
            try {
                const backupMode = this.getCurrentBackupMode();
                const allChats = await this.db.getAllTalkMessages();
                const metadata = {
                    userId: this.userId,
                    backupTime: new Date().toISOString(),
                    totalChats: allChats.length,
                    backupMode: backupMode
                };

                const backupData = {
                    metadata,
                    chats: allChats
                    // 注意：云端备份不包含配置信息，只有导出数据时才包含配置
                };

                const results = [];
                const errors = [];

                // WebDAV备份
                if (backupMode === 'webdav' || backupMode === 'both') {
                    const webdavConfig = this.webdav.getConfig();
                    if (webdavConfig) {
                        try {
                            const filename = await this.webdav.uploadBackup(backupData);
                            await this.webdav.cleanOldBackups();
                            results.push(`WebDAV备份成功: ${filename}`);
                            if (backupMode === 'both') {
                                this.ui.showToast(`✅ WebDAV备份成功: ${filename}`, 'success', 3000);
                            }
                        } catch (error) {
                            Utils.error('WebDAV备份失败', error);
                            errors.push(`WebDAV备份失败: ${error.message}`);
                            if (backupMode === 'both') {
                                this.ui.showToast(`❌ WebDAV备份失败: ${error.message}`, 'error', 5000);
                            }
                            if (backupMode === 'webdav') throw error;
                        }
                    } else if (backupMode === 'webdav') {
                        throw new Error('WebDAV未配置');
                    } else if (backupMode === 'both') {
                        this.ui.showToast('⚠️ WebDAV未配置，跳过WebDAV备份', 'warning', 3000);
                    }
                }

                // S3备份
                if (backupMode === 's3' || backupMode === 'both') {
                    const s3Config = this.s3.getConfig();
                    if (s3Config) {
                        try {
                            const filename = await this.s3.uploadBackup(backupData);
                            await this.s3.cleanOldBackups();
                            results.push(`S3备份成功: ${filename}`);
                            if (backupMode === 'both') {
                                this.ui.showToast(`✅ S3备份成功: ${filename}`, 'success', 3000);
                            }
                        } catch (error) {
                            Utils.error('S3备份失败', error);
                            errors.push(`S3备份失败: ${error.message}`);
                            if (backupMode === 'both') {
                                this.ui.showToast(`❌ S3备份失败: ${error.message}`, 'error', 5000);
                            }
                            if (backupMode === 's3') throw error;
                        }
                    } else if (backupMode === 's3') {
                        throw new Error('S3未配置');
                    } else if (backupMode === 'both') {
                        this.ui.showToast('⚠️ S3未配置，跳过S3备份', 'warning', 3000);
                    }
                }

                if (results.length === 0 && errors.length === 0) {
                    throw new Error('没有可用的备份配置');
                }

                GM_setValue(`last_backup_${this.userId}`, Date.now());

                // 组合结果消息
                let message = '';
                if (results.length > 0) {
                    message += results.join('; ');
                }
                if (errors.length > 0) {
                    if (message) message += '; ';
                    message += errors.join('; ');
                }

                Utils.log(`备份完成: ${message}`);

                if (backupMode !== 'both') {
                    if (errors.length > 0) {
                        throw new Error(errors.join('; '));
                    }
                } else {
                    if (results.length === 2) {
                        this.ui.showToast('🎉 双重备份全部成功！', 'success', 3000);
                    } else if (results.length === 1) {
                        this.ui.showToast('⚠️ 双重备份部分成功', 'warning', 3000);
                    } else if (results.length === 0) {
                        this.ui.showToast('❌ 双重备份全部失败', 'error', 5000);
                    }
                }

            } catch (error) {
                Utils.error('备份失败', error);
                throw error;
            }
        }

        /**
         * 测试S3连接
         * @param {S3Backup} s3Instance - S3备份实例
         * @returns {Promise<void>}
         */
        async testS3Connection(s3Instance) {
            const config = s3Instance.getConfig();
            if (!config) {
                throw new Error('S3配置未设置');
            }

            try {
                await s3Instance.testConnectionSimple();
            } catch (error) {
                Utils.error('S3连接测试失败', error);
                throw error;
            }
        }

        /**
         * 清空所有聊天数据
         * @param {ChatDB} db - 数据库实例，可选，默认使用this.db
         * @returns {Promise<void>}
         */
        async clearAllChatData(db = null) {
            try {
                const database = db || this.db;
                const transaction = database.db.transaction(['talk_messages'], 'readwrite');
                const store = transaction.objectStore('talk_messages');

                return new Promise((resolve, reject) => {
                    const request = store.clear();
                    request.onsuccess = () => {
                        Utils.debug('所有聊天记录已清空');
                        resolve();
                    };
                    request.onerror = () => reject(request.error);
                });
            } catch (error) {
                Utils.error('清空聊天数据失败', error);
                throw error;
            }
        }

        /**
         * 显示历史聊天记录
         * @returns {Promise<void>}
         */
        async showHistoryChats() {
            try {
                const allChats = await this.db.getAllTalkMessages();
                const modal = this.ui.showHistoryChats(allChats, this.showLatestChats);

                const debouncedBackupConfig = Utils.debounce(() => {
                    this.ui.showBackupConfig({ webdav: this.webdav, s3: this.s3 }, (backupMode) => {
                        Utils.log(`${backupMode.toUpperCase()}配置已保存`);
                        this.ui.showToast(`${backupMode.toUpperCase()}配置已保存`);
                    });
                }, 300);

                const debouncedBackupNow = Utils.debounce(async () => {
                    try {
                        await this.performBackup();
                    } catch (error) {
                        this.ui.showToast('备份失败: ' + error.message, 'error');
                    }
                }, 500);

                const debouncedRestore = Utils.debounce(() => {
                    this.showRestoreOptions();
                }, 300);

                modal.querySelector('#backup-config-btn').addEventListener('click', debouncedBackupConfig);
                modal.querySelector('#backup-now-btn').addEventListener('click', debouncedBackupNow);
                modal.querySelector('#restore-btn').addEventListener('click', debouncedRestore);

                modal.querySelector('#show-latest-toggle').addEventListener('change', (e) => {
                    this.showLatestChats = e.target.checked;
                    GM_setValue('show_latest_chats', this.showLatestChats);
                    this.ui.showToast(e.target.checked ? '已显示最新聊天' : '已隐藏最新聊天');
                    modal.remove();
                    this.showHistoryChats();
                });

            } catch (error) {
                Utils.error('显示历史聊天失败', error);
            }
        }

        /**
         * 显示恢复选项
         * @returns {Promise<void>}
         */
        async showRestoreOptions() {
            try {
                const backupMode = this.getCurrentBackupMode();

                if (backupMode === 'both') {
                    this.showRestoreSourceSelection();
                    return;
                }

                const restoreSource = backupMode === 's3' ? 's3' : 'webdav';
                await this.showBackupList(restoreSource);

            } catch (error) {
                Utils.error('显示恢复选项失败', error);
                this.ui.showToast('显示恢复选项失败: ' + error.message, 'error');
            }
        }

        /**
         * 显示恢复源选择
         */
        showRestoreSourceSelection() {
            const defaultRestoreSource = GM_getValue(`default_restore_source_${this.userId}`, 'webdav');

            const content = `
                <div style="margin-bottom: 16px; padding: 12px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; font-size: 14px;">
                    <strong>📋 选择恢复数据源</strong><br>
                    您配置了双重备份，请选择要从哪个存储恢复数据。
                </div>
                <div style="display: flex; gap: 12px; justify-content: center;">
                    <button class="nodeseek-btn" id="restore-from-webdav" style="padding: 12px 24px;">
                        从 WebDAV 恢复
                    </button>
                    <button class="nodeseek-btn" id="restore-from-s3" style="padding: 12px 24px; background: #17a2b8;">
                        从 S3 恢复
                    </button>
                </div>
                <div style="margin-top: 16px; text-align: center; font-size: 12px; color: #666;">
                    默认恢复源: ${defaultRestoreSource.toUpperCase()}
                </div>
            `;

            const modal = this.ui.createModal('选择恢复数据源', content, { width: '400px' });

            modal.querySelector('#restore-from-webdav').addEventListener('click', async () => {
                modal.remove();
                await this.showBackupList('webdav');
            });

            modal.querySelector('#restore-from-s3').addEventListener('click', async () => {
                modal.remove();
                await this.showBackupList('s3');
            });
        }

        /**
         * 显示指定存储的备份列表
         * @param {string} storageType - 存储类型 ('webdav' 或 's3')
         */
        async showBackupList(storageType) {
            try {
                Utils.debug(`正在获取${storageType.toUpperCase()}备份列表...`);
                const backupInstance = storageType === 's3' ? this.s3 : this.webdav;
                const backups = await backupInstance.listBackups();

                if (backups.length === 0) {
                    this.ui.showToast(`没有找到${storageType.toUpperCase()}备份文件`, 'warning');
                    return;
                }

                Utils.debug(`找到 ${backups.length} 个备份文件`);

                let content = `
                    <div style="margin-bottom: 16px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; font-size: 14px;">
                        <strong>⚠️ 重要提示：</strong>恢复操作会<strong>完全覆盖</strong>现有的本地聊天数据，原有数据将被删除且无法恢复！
                    </div>
                    <div style="margin-bottom: 12px; font-size: 14px; color: #666;">
                        恢复源: <strong>${storageType.toUpperCase()}</strong>
                    </div>
                    <div style="max-height: 300px; overflow-y: auto;">
                `;

                backups.forEach((backup, index) => {
                    const date = new Date(backup.lastModified).toLocaleString();
                    let fileName, backupKey;

                    if (storageType === 's3') {
                        fileName = backup.key.split('/').pop();
                        backupKey = backup.key;
                    } else {
                        fileName = backup.href.split('/').pop();
                        backupKey = backup.href;
                    }

                    content += `
                        <div style="padding: 12px; border-bottom: 1px solid #eee; cursor: pointer; transition: background 0.2s;"
                             data-backup="${backupKey}"
                             data-storage="${storageType}"
                             onmouseover="this.style.background='#f8f9fa'"
                             onmouseout="this.style.background='transparent'">
                            <div style="font-weight: 500; margin-bottom: 4px;">备份 ${index + 1}</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 2px;">时间: ${date}</div>
                            <div style="font-size: 11px; color: #999;">文件: ${fileName}</div>
                        </div>
                    `;
                });
                content += '</div>';

                const modal = this.ui.createModal(`选择要恢复的备份 (${storageType.toUpperCase()})`, content, { width: '500px' });

                modal.querySelectorAll('[data-backup]').forEach(item => {
                    item.addEventListener('click', async () => {
                        const backupKey = item.dataset.backup;
                        const storageType = item.dataset.storage;
                        const fileName = backupKey.split('/').pop();

                        // 确认对话框
                        if (confirm(`⚠️ 确定要从${storageType.toUpperCase()}恢复备份文件 "${fileName}" 吗？\n\n警告：此操作会完全覆盖现有的本地聊天数据！\n原有数据将被永久删除且无法恢复！\n\n请确认您真的要继续此操作。`)) {
                            modal.remove();

                            // 显示恢复进度
                            this.ui.showToast('正在恢复备份，请稍候...', 'info', 10000);

                            try {
                                await this.restoreFromBackup(backupKey, storageType);
                            } catch (error) {
                                Utils.error('恢复过程中出错', error);
                            }
                        }
                    });
                });

            } catch (error) {
                Utils.error('获取备份列表失败', error);

                let errorMessage = `获取${storageType.toUpperCase()}备份列表失败`;

                if (error.message.includes('401')) {
                    errorMessage = `${storageType.toUpperCase()}认证失败，请检查配置信息`;
                } else if (error.message.includes('403')) {
                    errorMessage = `${storageType.toUpperCase()}权限不足，请检查账户权限`;
                } else if (error.message.includes('404')) {
                    errorMessage = `${storageType.toUpperCase()}备份目录不存在`;
                } else if (error.message.includes('网络')) {
                    errorMessage = '网络连接失败，请检查网络连接';
                }

                this.ui.showToast(errorMessage, 'error', 5000);
            }
        }

        /**
         * 从备份恢复数据
         * @param {string} backupKey - 备份文件路径或S3键
         * @param {string} storageType - 存储类型 ('webdav' 或 's3')
         * @returns {Promise<void>}
         */
        async restoreFromBackup(backupKey, storageType) {
            try {
                const backupInstance = storageType === 's3' ? this.s3 : this.webdav;
                const config = backupInstance.getConfig();

                if (!config) {
                    throw new Error(`${storageType.toUpperCase()}配置未设置`);
                }

                let response;

                if (storageType === 's3') {
                    // S3恢复
                    Utils.debug(`正在从S3恢复备份: ${backupKey}`);
                    response = await this.s3.downloadBackup(backupKey);
                } else {
                    // WebDAV恢复
                    const url = this.webdav.buildFullUrl(backupKey);
                    Utils.debug(`正在从WebDAV恢复备份: ${url}`);

                    response = await new Promise((resolve, reject) => {
                        GM_xmlhttpRequest({
                            method: 'GET',
                            url: url,
                            headers: {
                                'Authorization': `Basic ${btoa(`${config.username}:${config.password}`)}`,
                                'Accept': 'application/json'
                            },
                            onload: (response) => {
                                Utils.debug(`恢复请求响应状态: ${response.status}`);

                                if (response.status >= 200 && response.status < 300) {
                                    try {
                                        const data = JSON.parse(response.responseText);
                                        resolve(data);
                                    } catch (parseError) {
                                        Utils.error(`解析备份文件失败: ${parseError.message}`);
                                        reject(new Error(`备份文件格式错误: ${parseError.message}`));
                                    }
                                } else {
                                    let errorMessage = `HTTP错误 ${response.status}`;

                                    switch (response.status) {
                                        case 401:
                                            errorMessage = '认证失败，请检查用户名和密码';
                                            break;
                                        case 403:
                                            errorMessage = '权限不足，无法访问备份文件';
                                            break;
                                        case 404:
                                            errorMessage = '备份文件不存在或已被删除';
                                            break;
                                        case 409:
                                            errorMessage = '文件访问冲突，请稍后重试';
                                            break;
                                        case 500:
                                            errorMessage = '服务器内部错误';
                                            break;
                                        default:
                                            errorMessage = `服务器返回错误: ${response.status} ${response.statusText}`;
                                    }

                                    reject(new Error(errorMessage));
                                }
                            },
                            onerror: (error) => {
                                Utils.error('网络请求失败', error);
                                reject(new Error('网络连接失败，请检查网络连接'));
                            },
                            ontimeout: () => {
                                reject(new Error('请求超时，请稍后重试'));
                            },
                            timeout: 30000
                        });
                    });
                }

                if (response && response.chats && Array.isArray(response.chats)) {
                    Utils.debug(`开始恢复 ${response.chats.length} 条聊天记录`);

                    // 完全覆盖模式：先清空现有数据
                    Utils.debug('清空现有聊天记录...');
                    await this.clearAllChatData();

                    let successCount = 0;
                    for (const chat of response.chats) {
                        try {
                            await this.db.saveTalkMessage(chat);
                            successCount++;
                        } catch (dbError) {
                            Utils.error(`保存聊天记录失败 (ID: ${chat.member_id})`, dbError);
                        }
                    }

                    const message = `恢复完成 (${storageType.toUpperCase()})，已覆盖本地数据，共恢复 ${successCount} 条聊天记录`;
                    Utils.log(message);
                    this.ui.showToast(message);
                } else {
                    throw new Error('备份文件格式不正确或不包含聊天数据');
                }

            } catch (error) {
                Utils.error('恢复备份失败', error);

                let userMessage = error.message;
                if (error.message.includes('409') || error.message.includes('冲突')) {
                    userMessage = '文件访问冲突，请稍后重试。如果问题持续存在，请检查服务器状态。';
                }

                this.ui.showToast(`恢复失败: ${userMessage}`, 'error', 5000);
            }
        }

        /**
         * 注册菜单命令
         */
        registerMenuCommands() {
            GM_registerMenuCommand('备份配置', () => {
                this.ui.showBackupConfig({ webdav: this.webdav, s3: this.s3 }, (storageType) => {
                    Utils.log(`${storageType.toUpperCase()}配置已保存`);
                    this.ui.showToast(`${storageType.toUpperCase()}配置已保存`);
                    this.performBackup();
                });
            });

            GM_registerMenuCommand('立即备份', async () => {
                try {
                    await this.performBackup();
                } catch (error) {
                    this.ui.showToast('备份失败: ' + error.message, 'error');
                }
            });

            GM_registerMenuCommand('历史聊天记录', () => {
                this.showHistoryChats();
            });
        }
    }

    /**
     * 全局变量
     */
    let chatBackup;

    /**
     * 初始化脚本
     */
    function initScript() {
        try {
            Utils.debug('脚本开始加载...');

            if (window.location.hostname !== 'www.nodeseek.com') {
                Utils.debug('不在NodeSeek域名，脚本不会运行');
                return;
            }

            chatBackup = new ChatBackup();
            window.chatBackup = chatBackup;

            if (document.readyState === 'loading') {
                Utils.debug('等待DOM加载完成...');
                document.addEventListener('DOMContentLoaded', () => {
                    Utils.debug('DOM加载完成，1秒后开始初始化');
                    setTimeout(() => chatBackup.init(), 1000);
                });
            } else {
                Utils.debug('DOM已加载，1秒后开始初始化');
                setTimeout(() => chatBackup.init(), 1000);
            }
        } catch (error) {
            Utils.error('脚本初始化失败', error);
        }
    }

    /**
     * 启动脚本
     */
    initScript();

})();
